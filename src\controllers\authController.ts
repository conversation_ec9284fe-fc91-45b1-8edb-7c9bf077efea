import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

export const register = async (req: Request, res: Response) => {
  const { email, password, fullName, role } = req.body;
  const hashed = await bcrypt.hash(password, 10);

  try {
    console.log("Registering user:", { email, password, fullName, role });
    const user = await prisma.user.create({
      data: { email, password: hashed, fullName, role },
    });
    res.json(user);
  } catch (e) {
    res.status(400).json({ message: "Registration failed", error: e });
  }
};
