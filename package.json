{"name": "bc-backend", "version": "1.0.0", "description": "", "main": "src/app.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev --respawn --transpile-only src/app.ts"}, "repository": {"type": "git", "url": "git+https://github.com/Fractioned-creator/bc-backend.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Fractioned-creator/bc-backend/issues"}, "homepage": "https://github.com/Fractioned-creator/bc-backend#readme", "dependencies": {"@prisma/client": "^6.12.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "prisma": "^6.12.0"}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}